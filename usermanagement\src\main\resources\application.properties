spring.application.name=usermanagement

# Server Configuration
server.port=8081

# Oracle Database Configuration
spring.datasource.url=************************************* 
spring.datasource.username=system
spring.datasource.password=Oracle2022
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Logging Configuration
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Jackson Configuration for JSON handling
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC
